import { Layer, LayerItem } from "@repo/core/components/layer";
import { Subtitle } from "@repo/core/guide/components/subtitle";
import { ComponentProps, FC } from "react";
import { AbsoluteFill, Video } from "remotion";
import { useGuideContext } from "../context/guide-context";

import lottie3dot from "@repo/core/assets/guide-theme/3dot.json";
import { cn } from "@repo/ui/lib/utils";
import Lottie from "lottie-react";
import { BtnGoto } from "./guide-line";
import { GuideSectionH2 } from "./guide-section-h2";

export const GuideCore: FC<ComponentProps<"div">> = () => {
  const {
    index,
    totalGuideCount,
    data,
    showSubtitle,
    refContainer,
    selectedLine,
    refFlipContainer,
    refFlipPrev,
    refFlipNext,
    needScrollFlip,
    showScrollFlipTip,
    client,
  } = useGuideContext();

  const { avatar, subtitles } = data;

  return (
    <AbsoluteFill>
      <div className="guide-view relative flex h-full w-full bg-[#FAF8F6]">
        <Layer className="font-resource-han-rounded">
          <LayerItem index={2} className="h-full w-full">
            <main
              ref={refFlipContainer}
              className={cn(
                "h-full w-full overflow-y-auto overscroll-contain scroll-smooth",
                needScrollFlip && "snap-y snap-proximity"
              )}
            >
              {needScrollFlip &&
                index > 0 && ( // 用于触发滚动到顶部
                  <>
                    <div
                      ref={refFlipPrev}
                      data-direction="prev"
                      className="text-text-4 flex h-20 w-full flex-col items-center justify-center text-[13px] font-normal"
                    >
                      {showScrollFlipTip ? (
                        "松手返回上一节"
                      ) : (
                        <Lottie
                          className="h-6 w-6"
                          animationData={lottie3dot}
                          autoPlay={true}
                          loop={true}
                        />
                      )}
                    </div>
                    {/* 回弹位置 */}
                    <div className="h-0.25 snap-start snap-normal" />
                  </>
                )}
              <div
                ref={refContainer}
                onClick={() => {
                  selectedLine.value = null;
                }}
                data-name="guide-container"
                className={cn(
                  "pointer-events-auto", // 这个地方是个Magic代码，可以避免某些情况出现滚动条
                  "relative h-full w-full overflow-y-auto scroll-smooth"
                )}
              >
                <GuideSectionH2 className="min-h-4/5" />
                <BtnGoto />
                <div className="h-1/5" />
              </div>
              {needScrollFlip &&
                index < totalGuideCount && ( // 用于触发滚动到底部
                  <>
                    {/* 回弹位置 */}
                    <div className="h-0.25 snap-end snap-normal" />
                    <footer
                      ref={refFlipNext}
                      data-name={index}
                      data-direction="next"
                      className="pointer-events-none flex h-20 w-full flex-col items-center justify-end pb-2 text-[13px] font-normal"
                    >
                      {showScrollFlipTip ? (
                        "松手进入下一节"
                      ) : (
                        <Lottie
                          className="h-6 w-6"
                          animationData={lottie3dot}
                          autoPlay={true}
                          loop={true}
                        />
                      )}
                    </footer>
                  </>
                )}
            </main>
          </LayerItem>
          <LayerItem
            index={1}
            className="max-w-1/5 right-0 w-[calc(100%-var(--width-guide))]"
          >
            <div className="relative flex h-full w-full flex-col items-center justify-end">
              {client !== "stu" && avatar.url && (
                <Video
                  src={avatar.url}
                  pauseWhenBuffering
                  crossOrigin="anonymous"
                  onError={(e) => {
                    console.log(e);
                  }}
                />
              )}
            </div>
          </LayerItem>
        </Layer>

        {showSubtitle && (
          <div className="right-30 fixed bottom-8 left-1/2 z-20 -translate-x-[50%]">
            <Subtitle subtitles={subtitles} />
          </div>
        )}
      </div>
    </AbsoluteFill>
  );
};
