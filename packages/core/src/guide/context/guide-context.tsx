"use client";
import {
  createContext,
  FC,
  ReactNode,
  RefObject,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
} from "react";

import { useThrottledCallback } from "use-debounce";

import { Signal, useComputed, useSignal } from "@preact-signals/safe-react";
import { MergedReference, Reference } from "@repo/core/types/data/comment";
import {
  GuideMode,
  GuideTheme,
  GuideWidgetData,
} from "@repo/core/types/data/widget-guide";
import { useGuideThemeViewmodel } from "../viewmodels/guide-theme-viewmodel";

type SelectedLine = {
  id: string;
  frame: number;
  left: number;
  top: number;
};

type GuideContextType = {
  client?: "stu" | "aipt" | "tch" | "";
  guideMode: GuideMode;
  title?: string;
  index: number;
  totalGuideCount: number;
  data: GuideWidgetData;
  theme: GuideTheme;
  showSubtitle: boolean;
  selectedLineId: string;
  selectedLine: Signal<SelectedLine | null>;
  onLineSelected: (frame: number) => void;
  handleLineClick: (
    left: number,
    top: number,
    lineId: string,
    frame: number
  ) => void;
  refContainer: RefObject<HTMLDivElement | null>;
  selectable: boolean;

  commentRef?: Signal<HTMLDivElement | null>;
  lineIdInRange?: string;
  referenceList?: Reference[];
  onClickReference?: (
    reference: MergedReference[string][string][number]
  ) => void;

  refFlipContainer: RefObject<HTMLDivElement | null>;
  refFlipPrev: RefObject<HTMLDivElement | null>;
  refFlipNext: RefObject<HTMLDivElement | null>;
  needScrollFlip: boolean;
  showScrollFlipTip: boolean;
};

const GuideContext = createContext<GuideContextType>({} as GuideContextType);

const useGuideContext = () => useContext(GuideContext);

interface GuideProviderProps {
  client?: "stu" | "aipt" | "tch" | "";
  guideMode?: GuideMode;
  title?: string;
  index?: number;
  totalGuideCount?: number;
  data: GuideWidgetData;
  theme?: GuideTheme;
  showSubtitle?: boolean;
  refContainer?: RefObject<HTMLDivElement | null>;
  onLineClick?: (frame: number) => void;
  children: ReactNode;
  selectable?: boolean;
  onScrollFlip?: (index: number) => void;
  commentRef?: Signal<HTMLDivElement | null>;
  lineIdInRange?: string;
  referenceList?: Reference[];
  onClickReference?: (
    reference: MergedReference[string][string][number]
  ) => void;
}

const GuideProvider: FC<GuideProviderProps> = ({ children, ...props }) => {
  const {
    client = "",
    guideMode = GuideMode.follow,
    title,
    index = 0,
    totalGuideCount = 0,
    data,
    theme: themeConfig,
    showSubtitle,
    onLineClick,
    selectable = true,
    commentRef,
    lineIdInRange,
    referenceList,
    onClickReference,
    refContainer,
    onScrollFlip,
  } = props;

  const theme = useGuideThemeViewmodel(themeConfig);
  const selectedLine = useSignal<SelectedLine | null>(null);
  const selectedLineId = useComputed(() => selectedLine.value?.id ?? "");

  const refFlipContainer = useRef<HTMLDivElement | null>(null);
  const refFlipPrev = useRef<HTMLDivElement>(null);
  const refFlipNext = useRef<HTMLDivElement>(null);
  const needScrollFlip = useMemo(() => {
    return onScrollFlip !== undefined;
  }, [onScrollFlip]);

  const scrollToWidgetIndex = useSignal<number | null>(null);
  const showScrollFlipTip = useComputed(() => {
    return scrollToWidgetIndex.value !== null;
  });

  const handleLineClick = useCallback(
    (left: number, top: number, id: string, frame: number) => {
      if (!selectable) return;
      selectedLine.value = {
        id,
        frame,
        left,
        top,
      };
    },
    [selectable, selectedLine]
  );

  const onLineSelected = useCallback(
    (frame: number) => {
      onLineClick?.(frame);
    },
    [onLineClick]
  );

  useEffect(() => {
    if (!refFlipContainer?.current) {
      return;
    }
    const container = refFlipContainer.current;
    const options = {
      root: container,
      rootMargin: "0px",
      threshold: 0.98,
      delay: 400,
    };
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        const target = entry.target as HTMLDivElement;
        if (entry.isIntersecting) {
          const direction = target.dataset.direction;
          if (direction === "next") {
            scrollToWidgetIndex.value = index + 1;
          } else if (direction === "prev") {
            scrollToWidgetIndex.value = index - 1;
          }
        } else {
          scrollToWidgetIndex.value = null;
        }
      });
    }, options);
    if (refFlipNext.current) {
      observer.observe(refFlipNext.current);
    }
    if (refFlipPrev.current) {
      observer.observe(refFlipPrev.current);
    }
    return () => {
      observer.disconnect();
    };
  }, [refFlipContainer, refFlipNext, refFlipPrev, index, scrollToWidgetIndex]);

  const actionEnd = useThrottledCallback(() => {
    const index = scrollToWidgetIndex.value;
    if (index === null) {
      return;
    }
    onScrollFlip?.(index);
    scrollToWidgetIndex.value = null;
  }, 200);

  useEffect(() => {
    if (!refContainer?.current) {
      return;
    }

    const container = refContainer.current;
    container.addEventListener("wheel", actionEnd);
    container.addEventListener("touchend", actionEnd);
    return () => {
      container.removeEventListener("wheel", actionEnd);
      container.removeEventListener("touchend", actionEnd);
    };
  }, [refContainer, scrollToWidgetIndex, onScrollFlip, actionEnd]);

  const value = {
    client,
    guideMode,
    title,
    index,
    totalGuideCount,
    data,
    theme,
    showSubtitle: showSubtitle ?? true,
    selectedLine,
    selectedLineId: selectedLineId.value,
    onLineSelected,
    handleLineClick,
    refContainer: refContainer ?? { current: null },
    selectable,
    refFlipContainer,
    refFlipPrev,
    refFlipNext,
    needScrollFlip,
    showScrollFlipTip: showScrollFlipTip.value,
    commentRef,
    lineIdInRange,
    referenceList,
    onClickReference,
  };
  return <GuideContext value={value}>{children}</GuideContext>;
};

export {
  GuideContext,
  GuideProvider,
  useGuideContext,
  type GuideContextType,
  type GuideProviderProps,
};
