import IconSidebarChecked from "@repo/core/assets/course/sidebar-checked.svg";
import IconSidebarCurrent from "@repo/core/assets/course/sidebar-current.svg";
import IconSidebarFlag from "@repo/core/assets/course/sidebar-flag.svg";
import IconSidebarLocked from "@repo/core/assets/course/sidebar-locked.svg";
import { WidgetStatus } from "@repo/core/types/data/course";
import { cn } from "@repo/ui/lib/utils";
import { ComponentProps, FC, useMemo } from "react";

interface CourseSummaryProps extends ComponentProps<"div"> {
  index: number;
  title: string;
  status: WidgetStatus;
  isCurrent: boolean;
  isFirst: boolean;
  isLast: boolean;
  onSelect?: () => void;
}

export const CourseSummary: FC<CourseSummaryProps> = ({
  title,
  status,
  isCurrent,
  isFirst,
  isLast,
  onSelect,
}) => {
  const isLocked = status === "locked";
  const isCompleted = status === "completed";

  const Icon = useMemo(() => {
    if (isLast) return IconSidebarFlag;
    if (isCompleted) return IconSidebarChecked;
    if (isCurrent) return IconSidebarCurrent;

    return IconSidebarCurrent;
  }, [isCurrent, isCompleted, isLast]);

  return (
    <div
      className={cn("flex select-none flex-row")}
      onClick={(e) => {
        e.stopPropagation();
        if (isLocked) return;
        onSelect?.();
      }}
    >
      <div
        className={cn(
          "relative flex w-5 flex-col items-center justify-start",
          isLocked && "opacity-30"
        )}
      >
        <div className={cn("absolute", isFirst ? "-top-1" : "top-3")}>
          <Icon className="text-main-orange" />
        </div>
        <div
          className={cn(
            "border-0.5 border-main-orange border",
            isLast ? "h-1" : "h-full",
            isLocked && "border-dashed"
          )}
        />
      </div>
      <div
        className={cn(
          "flex h-full min-h-16 w-full flex-col p-3",
          isCurrent && "rounded-lg bg-[#FFA666]/10"
        )}
      >
        {!isLocked ? (
          <h2
            className={cn(
              "text-base font-bold leading-snug",
              isCurrent ? "text-dim-orange" : "text-text-1"
            )}
          >
            {title}
          </h2>
        ) : (
          <div className="flex w-full flex-row items-center justify-evenly gap-3">
            <h2 className="text-text-4 flex-1 text-base font-bold leading-snug">
              {title}
            </h2>
            <IconSidebarLocked className="text-text-4" />
          </div>
        )}
      </div>
    </div>
  );
};
