import { CourseSummary } from "@repo/core/course/course-summary";
import { cn } from "@repo/ui/lib/utils";
import { CourseWidgetSummary } from "../type";

export interface CourseProgressBarProps {
  className?: string;

  activeIndex: number;
  courseWidgetSummary: CourseWidgetSummary[];
  onChange?: (index: number) => void;
}

export const CourseProgressBar = ({
  activeIndex,
  courseWidgetSummary,
  onChange,

  className = "",
}: CourseProgressBarProps) => {
  return (
    <div className={cn("h-full cursor-pointer overflow-y-auto", className)}>
      <div className="flex h-auto flex-col">
        {courseWidgetSummary.map((summary, idx) => (
          <CourseSummary
            key={idx}
            index={idx}
            title={summary.name || "暂无标题"}
            status={summary.status || "unlocked"}
            onSelect={() => onChange?.(idx)}
            isCurrent={idx === activeIndex}
            isFirst={idx === 0}
            isLast={idx === courseWidgetSummary.length - 1}
          />
        ))}
      </div>
    </div>
  );
};
