import { TouchEvent, useRef } from "react";

export const useTap = (
  callback: (e: TouchEvent) => void,
  maxMoveDistance: number = 10
) => {
  const isTapping = useRef(false);
  const startPosition = useRef<{ x: number; y: number } | null>(null);

  const handleTouchStart = (e: TouchEvent) => {
    e.preventDefault();
    isTapping.current = true;
    const touch = e.touches[0];
    startPosition.current = { x: touch.clientX, y: touch.clientY };
  };

  const handleTouchMove = (e: TouchEvent) => {
    e.preventDefault();
    if (!isTapping.current || !startPosition.current) return;

    const touch = e.touches[0];
    const deltaX = Math.abs(touch.clientX - startPosition.current.x);
    const deltaY = Math.abs(touch.clientY - startPosition.current.y);
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    // 如果移动距离超过阈值，则取消tap事件
    if (distance > maxMoveDistance) {
      isTapping.current = false;
    }
  };

  const handleTouchEnd = (e: TouchEvent) => {
    e.preventDefault();
    if (isTapping.current) {
      callback(e);
    }
    isTapping.current = false;
    startPosition.current = null;
  };

  return {
    onTouchStart: handleTouchStart,
    onTouchEnd: handleTouchEnd,
    onTouchMove: handleTouchMove,
  };
};
