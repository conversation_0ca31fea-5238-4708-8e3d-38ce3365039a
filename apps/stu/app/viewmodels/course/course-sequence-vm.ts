"use client";
import { useCourseModel } from "@/app/models/course-model";
import { LocalCourseProgress } from "@/app/models/local-progress";
import { finishLesson, surveillanceReport } from "@/app/utils/device";
import { post } from "@/app/utils/fetcher";
import {
  CourseWidget,
  CourseWidgetSummary,
  CourseWidgetSummaryWithoutStatus,
  WidgetStatus,
} from "@/types/app/course";
import {
  Signal,
  useComputed,
  useSignal,
  useSignalEffect,
} from "@preact-signals/safe-react";
import { useCallback, useEffect, useMemo } from "react";
import useSWRMutation from "swr/mutation";

interface WidgetLoaderItem {
  key: string;
  data: CourseWidget;
}

type CourseSequenceViewmodel = {
  /**
   * 是否版本变更
   */
  isVersionChanged: Signal<boolean>;
  /**
   * 是否加载中
   */
  isLoading: boolean;
  /**
   * 知识点ID
   */
  knowledgeId: number;
  /**
   * 课程ID
   */
  lessonId: number;
  /**
   * 当前index
   */
  currentIndex: Signal<number>;
  /**
   * 当前组件
   */
  currentWidget: CourseWidgetSummary | undefined;
  /**
   * 组件摘要序列, 用于进度显示
   */
  widgetSummarySequence: CourseWidgetSummary[];

  widgetList: CourseWidgetSummaryWithoutStatus[];
  /**
   * 总页数
   */
  total: number;
  /**
   * 按index跳转组件
   */
  goto: (index: number) => void;
  /**
   * 下一页
   */
  next: () => void;
  /**
   * 上一页
   */
  prev: () => void;
  /**
   * 错误信息
   */
  error: Error | undefined;
  /**
   * 报告进度
   */
  reportProgress: (params: {
    widgetIndex: number;
    costSeconds: number;
    status: "locked" | "completed";
  }) => Promise<void>;
  /**
   * 需上报的耗时
   */
  reportCostTime: (n: number) => void;
  /**
   * 本地进度记录器
   */
  localProgressRecorder: LocalCourseProgress;
  /**
   * 是否显示动画
   */
  showAnimation: Signal<boolean>;
  /**
   * 完成动画
   */
  completeAnimation: () => void;
  /**
   * 滚动行为
   */
  scrollBehavior: Signal<"smooth" | "instant">;
  /**
   * 下一题参数
   */
  nextQuestionParams: string;
  /**
   * 课程版本
   */
  lessonVersion: string;
};

const useCourseSequenceViewmodel = (
  knowledgeId: number,
  redirectWidgetIndex?: string
) => {
  // 获取课程信息
  const { data, isLoading, error } = useCourseModel(knowledgeId);
  const {
    lessonId = 0,
    total = 0,
    widgets = [],
    nextQuestionParams = "",
    currentWidgetIndex = 0,
    lessonVersion = "",
  } = data ?? {};

  const isVersionChanged = useSignal(false);
  const currentIndex = useSignal(0);
  // 保存目标index
  const targetIndex = useSignal(0);
  const scrollBehavior = useSignal<"smooth" | "instant">("instant");
  const widgetSummaries = useSignal<CourseWidgetSummary[]>([]);
  const widgetList = useSignal<CourseWidgetSummaryWithoutStatus[]>([]);
  const currentWidget = useComputed(() => {
    return widgetSummaries.value[currentIndex.value];
  });
  const {
    trigger: doReportProgress,
    isMutating: isReporting,
    data: reportProgressData,
  } = useSWRMutation("/api/v1/lesson/progress/report", post);

  const widgetCostTime = useSignal(new Map<number, number>());
  const localProgressRecorder = useMemo(
    () => new LocalCourseProgress(knowledgeId, lessonId),
    [knowledgeId, lessonId]
  );

  const showAnimation = useSignal(false);

  const reportCostTime = useCallback(
    (n: number) => {
      widgetCostTime.value.set(currentIndex.value, n);
    },
    [currentIndex, widgetCostTime]
  );
  const reportProgress = useCallback(
    async ({
      widgetIndex,
      costSeconds,
      status,
    }: {
      widgetIndex: number;
      costSeconds: number;
      status: WidgetStatus;
    }) => {
      surveillanceReport({
        operationType: 4,
        learningProgress:
          ((widgetIndex + 1) / widgetSummaries.value.length) * 100,
        studyTime: Array.from(widgetCostTime.value.values()).reduce(
          (acc, cur) => acc + cur,
          0
        ),
        knowledgePointId: knowledgeId,
      });
      await doReportProgress({
        knowledgeId,
        lessonId,
        widgetIndex,
        costSecond: costSeconds,
        status,
      });
    },
    [
      doReportProgress,
      knowledgeId,
      lessonId,
      widgetCostTime.value,
      widgetSummaries.value.length,
    ]
  );

  const completeAnimation = useCallback(() => {
    console.log(
      "4 next::completeAnimation",
      currentIndex.value,
      targetIndex.value
    );
    const current = currentIndex.value;
    const target = targetIndex.value;
    // 更新widgetSummarySequence
    widgetSummaries.value = widgetSummaries.value.map((it) => {
      if (it.index === current && it.status !== "completed") {
        return { ...it, status: "completed" };
      }
      if (it.index === target && it.status === "locked") {
        return { ...it, status: "unlocked" };
      }
      return it;
    });
    showAnimation.value = false;
    // 更新currentIndex
    currentIndex.value = targetIndex.value;

    console.log("5 next::----done----");
  }, [widgetSummaries, currentIndex, targetIndex.value, showAnimation]);

  const goto = useCallback(
    (target: number) => {
      console.log("1 next::GOTO", target);
      if (isReporting) return;

      const current = currentIndex.value;
      console.log("1-1 next::isReporting", isReporting, current);
      if (target < 0 || target === current) {
        return;
      }
      // 下一个才上报
      if (target === current + 1) {
        reportProgress({
          widgetIndex: current,
          costSeconds: widgetCostTime.value.get(current) ?? 1,
          status: "completed",
        }).then(() => {
          console.log("next::reportProgress done");
        });
      }
      // 如果target大于total，则直接完成
      if (target >= total) {
        console.log("5 next::----THE END----");
        finishLesson();
        return;
      }
      targetIndex.value = target;
      console.log("1-2 next::targetIndex", target);
      //
      scrollBehavior.value =
        Math.abs(target - current) === 1 ? "smooth" : "instant";
      // 判断是否需要播放动效// 判断是否需要播放动效
      const currentWidget = widgetSummaries.value[current];
      const targetWidget = widgetSummaries.value[target];
      console.log(
        "1-3 next::from",
        currentWidget?.index,
        "to: ",
        targetWidget?.index
      );
      if (currentWidget?.type !== targetWidget?.type) {
        console.log(
          "1-4 next::showAnimation",
          currentWidget?.type,
          targetWidget?.type
        );
        showAnimation.value = true;
      } else {
        completeAnimation();
      }
    },
    [
      isReporting,
      total,
      currentIndex.value,
      targetIndex.value,
      showAnimation,
      completeAnimation,
      widgetSummaries,
      reportProgress,
      widgetCostTime,
      scrollBehavior.value,
    ]
  );

  const next = useCallback(() => {
    goto(currentIndex.value + 1);
  }, [goto, currentIndex]);

  const prev = useCallback(() => {
    goto(currentIndex.value - 1);
  }, [goto, currentIndex]);

  useEffect(() => {
    console.log("**next::redirectWidgetIndex", redirectWidgetIndex);
    currentIndex.value = redirectWidgetIndex
      ? Number(redirectWidgetIndex)
      : currentWidgetIndex;
  }, [currentWidgetIndex, currentIndex, redirectWidgetIndex]);

  useEffect(() => {
    if (isLoading) return;
    if (!widgets || widgets.length === 0) return;
    if (widgetSummaries.value.length === 0) {
      widgetSummaries.value = [...widgets];
    }
    if (widgetList.value.length === 0) {
      widgetList.value = widgets.map((it) => {
        const { status, ...rest } = it;
        return rest;
      });
    }
  }, [isLoading, widgets, widgetSummaries]);

  useEffect(() => {
    if (isReporting) return;
    if (reportProgressData) {
      // 判断版本是否更新
      isVersionChanged.value =
        lessonVersion !==
        (reportProgressData as { lessonVersion: string }).lessonVersion;
    }
  }, [isReporting, lessonVersion, reportProgressData, isVersionChanged]);

  useSignalEffect(() => {
    if (isVersionChanged.value) {
      localProgressRecorder.clearAll();
    }
  });

  return {
    isVersionChanged,
    knowledgeId,
    lessonId,
    total,
    currentIndex,
    currentWidget: currentWidget.value,
    widgetSummarySequence: widgetSummaries.value,
    widgetList: widgetList.value,
    next,
    prev,
    goto,
    error: error as Error,
    reportProgress,
    reportCostTime,
    localProgressRecorder,
    showAnimation,
    completeAnimation,
    isLoading,
    nextQuestionParams,
    lessonVersion,
    scrollBehavior,
  };
};

export {
  useCourseSequenceViewmodel,
  type CourseSequenceViewmodel,
  type WidgetLoaderItem,
};
