import { toast } from "@/app/components/common/toast";
import { useSelection } from "@/app/hooks/use-selection";
import { useReferencesList } from "@/app/models/comments/comments-model";
import { ApiGetReferencesResponse } from "@/app/models/comments/schemas";
import { surveillanceReport } from "@/app/utils/device";
import { GuideProgress } from "@/types/app/course";
import { WidgetViewProps } from "@/types/app/ui";
import {
  batch,
  ReadonlySignal,
  Signal,
  useComputed,
  useSignal,
  useSignalEffect,
} from "@preact-signals/safe-react";
import { CallbackListener, PlayerRef } from "@remotion/player";
import { VolcenginePlayer } from "@repo/core/components/volcengine-video/volcengine-video";
import { MergedReference } from "@repo/core/types/data/comment";
import { GuideWidgetData } from "@repo/core/types/data/widget-guide";

import {
  createContext,
  FC,
  RefObject,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
} from "react";
import { KeyedMutator } from "swr";
import {
  CourseViewContextType,
  GuideMode,
  useCourseViewContext,
} from "../course/course-view-context";
import { useGuideGesture } from "./hooks/use-guide-gesture";

export type GuideViewContextType = CourseViewContextType & {
  title?: string;
  index?: number;
  totalGuideCount: number;
  data: GuideWidgetData;
  refPlayer: RefObject<PlayerRef | null>;
  refVolcenginePlayer: Signal<VolcenginePlayer | null>;
  canPlay: boolean;
  isPlaying: boolean;
  forwardTo: (seconds: number) => void;
  seekTo: (frame: number) => void;
  togglePlay: () => void;
  setFollowMode: () => void;
  setFreeMode: () => void;
  showPlayerControls: boolean;
  togglePlayerControls: () => void;
  hidePlayerControls: () => void;
  set3XPlayRate: () => void;
  resetPlayRate: () => void;
  progress: GuideProgress;
  commentsBarVisible: ReadonlySignal<boolean>;
  commentInputVisible: Signal<boolean>;
  commentRef: Signal<HTMLDivElement | null>;
  ranges: ReturnType<typeof useSelection>["ranges"];
  contextMenuPos: ReturnType<typeof useSelection>["pos"];
  referenceList?: ApiGetReferencesResponse["list"];
  refreshReferences: KeyedMutator<ApiGetReferencesResponse>;
  markedRanges: Signal<ReturnType<typeof useSelection>["ranges"]>;
  markedReference: Signal<MergedReference[string][string][number] | null>;
  onClickReference: (
    reference: MergedReference[string][string][number]
  ) => void;
  refContainer: RefObject<HTMLDivElement | null>; //文稿容器, 用户处理
  active: boolean;
  durationInFrames: number;
};

const GuideViewContext = createContext<GuideViewContextType>(
  {} as GuideViewContextType
);

interface GuideViewContextProviderProps extends WidgetViewProps<"guide"> {
  children: React.ReactNode;
}

export const useGuideViewContext = () => {
  return useContext(GuideViewContext);
};

export const GuideViewContextProvider: FC<GuideViewContextProviderProps> = ({
  totalGuideCount,
  content,
  // active,
  children,
}) => {
  const courseContexts = useCourseViewContext();

  const {
    lessonId,
    guideMode,
    isProgressBarOpen,
    playRate,
    next,
    localProgressRecorder,
    reportCostTime,
    isVersionChanged,
    knowledgeName,
    currentIndex,
    enableComment,
  } = courseContexts;
  const { data, name: title, index } = content;
  const isActive = useComputed(() => {
    return index === currentIndex.value;
  });

  const fps = data.avatar?.fps ?? 24;
  const durationInFrames = data.avatar?.durationInFrames + fps; //增加1秒, bugfix: 最后一点语音未播完就结束
  const videoDuration = durationInFrames / fps;
  const { data: referenceList, refresh: refreshReferences } = useReferencesList(
    enableComment
      ? {
          objId: lessonId,
          objType: 100,
        }
      : undefined
  );

  const progress = useMemo(() => {
    const p = localProgressRecorder.load<GuideProgress>(index, {
      frame: 0,
    });
    // 处理本地进度和视频时长不一致的情况(比如视频时长因课程变更而变化), 清除本地进度
    if (p.frame >= durationInFrames) {
      p.frame = 0;
      localProgressRecorder.clearAll();
    }
    return p;
  }, [index, localProgressRecorder, durationInFrames]);

  const refPlayer = useRef<PlayerRef | null>(null);
  const refVolcenginePlayer = useSignal<VolcenginePlayer | null>(null);
  const canPlay = useSignal(false);
  const refContainer = useRef<HTMLDivElement | null>(null);

  const isPlaying = useSignal(false);
  const isPlayingBeforeOpenProgressBar = useSignal(false);
  const isPlayingBeforeDocumentHidden = useSignal(false);
  const isPlayingBeforeCommentBarOpen = useSignal(false);
  const isPlayingBeforeCommentInputOpen = useSignal(false);
  const showPlayerControls = useSignal(false);

  const playRateBeforeLongPress = useSignal(playRate.value);

  const lastTime = useSignal(Date.now());
  const costTime = useSignal(0);
  const commentInputVisible = useSignal(false);
  const commentRef = useSignal<HTMLDivElement | null>(null);
  const { ranges, pos } = useSelection();
  const markedRanges = useSignal<typeof ranges>([]);
  const markedReference = useSignal<
    MergedReference[string][string][number] | null
  >(null);
  const commentsBarVisible = useComputed(() => Boolean(markedReference.value));

  const onClickReference = useCallback(
    (ref: MergedReference[string][string][number]) => {
      markedReference.value = ref;
    },
    [markedReference]
  );

  const surveillanceReportHandler = useCallback(
    (currentFrame: number, videoAction: "seek" | "pause" | "play" | "stop") => {
      surveillanceReport({
        operationType: 6,
        videoAction,
        videoPosition: currentFrame / fps,
        videoDuration,
        currentStudyContent: knowledgeName,
      });
    },
    [fps, knowledgeName, videoDuration]
  );

  useEffect(() => {
    if (isProgressBarOpen) {
      batch(() => {
        isPlayingBeforeOpenProgressBar.value = isPlaying.peek();
        isPlaying.value = false;
      });
    } else {
      isPlaying.value = isPlayingBeforeOpenProgressBar.peek();
    }
  }, [isProgressBarOpen, isPlaying, isPlayingBeforeOpenProgressBar]);

  useSignalEffect(() => {
    if (commentsBarVisible.value) {
      batch(() => {
        isPlayingBeforeCommentBarOpen.value = isPlaying.peek();
        isPlaying.value = false;
      });
    } else {
      isPlaying.value = isPlayingBeforeCommentBarOpen.peek();
    }
  });

  useSignalEffect(() => {
    if (commentInputVisible.value) {
      batch(() => {
        isPlayingBeforeCommentInputOpen.value = isPlaying.peek();
        isPlaying.value = false;
      });
    } else {
      isPlaying.value = isPlayingBeforeCommentInputOpen.peek();
    }
  });

  // 按时间跳转
  const forwardTo = useCallback(
    (seconds: number) => {
      if (!isActive.value) return;
      if (!refPlayer.current) {
        return;
      }
      if (!refVolcenginePlayer.value) {
        return;
      }
      const player = refPlayer.current;
      const currentFrame = player.getCurrentFrame() + seconds * fps;
      surveillanceReportHandler(currentFrame, "seek");
      player.seekTo(currentFrame);
      const vodPlayer = refVolcenginePlayer.value;
      vodPlayer.seek(vodPlayer.currentTime + seconds);
    },
    [
      fps,
      surveillanceReportHandler,
      refVolcenginePlayer.value,
      isPlaying.value,
      isActive.value,
    ]
  );

  // 按帧跳转
  const seekTo = useCallback(
    (frame: number) => {
      if (!isActive.value) return;
      if (!refPlayer.current) {
        return;
      }
      if (frame >= durationInFrames || frame < 0) {
        return;
      }
      const player = refPlayer.current;
      surveillanceReportHandler(frame, "seek");
      player.seekTo(frame);
      refVolcenginePlayer.value?.seek(frame / fps);
      isPlaying.value = true;
    },
    [
      surveillanceReportHandler,
      isPlaying.value,
      durationInFrames,
      fps,
      refVolcenginePlayer.value,
      isActive.value,
    ]
  );

  const togglePlay = useCallback(() => {
    if (guideMode.value === GuideMode.free) return;
    isPlaying.value = !isPlaying.peek();
    if (isPlaying.value === false) {
      surveillanceReportHandler(
        refPlayer.current?.getCurrentFrame() || 0,
        "pause"
      );
    }
  }, [guideMode, isPlaying, surveillanceReportHandler]);

  const setFollowMode = useCallback(() => {
    if (guideMode.value === GuideMode.follow) return;
    guideMode.value = GuideMode.follow;
    isPlaying.value = true;
  }, [guideMode, isPlaying]);

  const setFreeMode = useCallback(() => {
    if (guideMode.value === GuideMode.free) return;
    guideMode.value = GuideMode.free;
    isPlaying.value = false;
    toast.show("自由浏览课件");
  }, [guideMode, isPlaying]);

  const togglePlayerControls = useCallback(() => {
    showPlayerControls.value = !showPlayerControls.peek();
  }, [showPlayerControls]);

  const hidePlayerControls = useCallback(() => {
    showPlayerControls.value = false;
  }, [showPlayerControls]);

  const set3XPlayRate = useCallback(() => {
    if (guideMode.value === GuideMode.free) return;
    if (playRate.value === 3) return;

    playRateBeforeLongPress.value = playRate.value;
    playRate.value = 3;
  }, [guideMode, playRate, playRateBeforeLongPress]);

  const resetPlayRate = useCallback(() => {
    if (playRate.value === playRateBeforeLongPress.value) return;
    playRate.value = playRateBeforeLongPress.value;
  }, [playRate, playRateBeforeLongPress]);

  // refPlayer 事件
  useEffect(() => {
    if (!refPlayer.current) {
      return;
    }
    const player = refPlayer.current;

    //the event is throttled to only fire a few times a second at most every 250ms.
    const handleTimeUpdate: CallbackListener<"timeupdate"> = (e) => {
      localProgressRecorder.save(index, {
        frame: e.detail.frame,
      });
    };

    const handleEnded: CallbackListener<"ended"> = () => {
      console.log("ended", index);
      // 清除进度
      surveillanceReportHandler(player.getCurrentFrame(), "stop");
      localProgressRecorder.clear(index);
      isPlaying.value = false;
      next();
    };

    const handleVisibilitychange = () => {
      if (document.visibilityState === "hidden") {
        batch(() => {
          isPlayingBeforeDocumentHidden.value = isPlaying.peek();
          isPlaying.value = false;
        });
      } else {
        isPlaying.value = isPlayingBeforeDocumentHidden.peek();
      }
    };
    player.addEventListener("timeupdate", handleTimeUpdate);
    player.addEventListener("ended", handleEnded);
    document.addEventListener("visibilitychange", handleVisibilitychange);

    return () => {
      player.removeEventListener("timeupdate", handleTimeUpdate);
      player.removeEventListener("ended", handleEnded);
      document.removeEventListener("visibilitychange", handleVisibilitychange);
    };
  }, [
    isPlaying,
    playRateBeforeLongPress,
    next,
    localProgressRecorder,
    index,
    isPlayingBeforeDocumentHidden,
    playRate,
    surveillanceReportHandler,
  ]);

  useEffect(() => {
    const player = refVolcenginePlayer.value;

    if (!player) return;

    const handleWaiting = () => {
      canPlay.value = false;
    };
    const handlePlaying = () => {
      canPlay.value = true;
    };
    const handleCanplay = () => {
      canPlay.value = true;
    };
    const handleError = () => {
      console.log("====error");
      canPlay.value = false;
    };
    const handleSeeked = () => {
      // console.log("seeked", player.currentTime, player.duration);
      isPlaying.value = true;
    };
    player.addEventListener("seeked", handleSeeked);
    player.addEventListener("waiting", handleWaiting);
    player.addEventListener("playing", handlePlaying);
    player.addEventListener("canplay", handleCanplay);
    player.addEventListener("error", handleError);
    return () => {
      player.removeEventListener("seeked", handleSeeked);
      player.removeEventListener("waiting", handleWaiting);
      player.removeEventListener("playing", handlePlaying);
      player.removeEventListener("canplay", handleCanplay);
      player.removeEventListener("error", handleError);
    };
  }, [refVolcenginePlayer.value, isPlaying]);

  useEffect(() => {
    if (refVolcenginePlayer.value === null) return;
    const player = refVolcenginePlayer.value;
    const handleLoadedMetadata = () => {
      const videoFrames = player.duration * data.avatar?.fps;
      const guideFrames = data.avatar?.durationInFrames + data.avatar?.fps;
      if (videoFrames > guideFrames) {
        console.warn(
          "数字人时长大于文稿时长: 数字人帧数",
          videoFrames,
          "文稿帧数",
          guideFrames
        );
      }
    };
    player.addEventListener("loadedmetadata", handleLoadedMetadata);

    return () => {
      player.removeEventListener("loadedmetadata", handleLoadedMetadata);
    };
  }, [refVolcenginePlayer.value, data.avatar?.durationInFrames]);

  useSignalEffect(() => {
    if (isActive.value && guideMode.value === GuideMode.follow) {
      isPlaying.value = true;
    } else {
      isPlaying.value = false;
    }
  });

  useSignalEffect(() => {
    if (isVersionChanged.value) {
      isPlaying.value = false;
    }
  });

  useSignalEffect(() => {
    if (refVolcenginePlayer.value === null) return;
    if (isActive.value && isPlaying.value && canPlay.value) {
      refPlayer.current?.play();
      refVolcenginePlayer.value?.play();
      surveillanceReportHandler(
        refPlayer.current?.getCurrentFrame() || 0,
        "play"
      );
    } else {
      refPlayer.current?.pause();
      refVolcenginePlayer.value?.pause();
    }
  });

  useEffect(() => {
    if (isActive.value) {
      lastTime.value = Date.now();
    }
  }, [isActive.value, lastTime]);

  useEffect(() => {
    if (!isActive.value) return;

    const updateTime = () => {
      const now = Date.now();
      costTime.value += now - lastTime.value;
      lastTime.value = now;
      // console.log({ duration: costTime.value });
      reportCostTime(costTime.value);
    };
    const timer = setInterval(updateTime, 250);
    return () => {
      clearInterval(timer);
      updateTime();
    };
  }, [isActive.value, reportCostTime, lastTime, costTime]);

  useGuideGesture(refContainer, setFreeMode);

  const value = {
    ...courseContexts,
    refPlayer,
    refVolcenginePlayer,
    canPlay: canPlay.value,
    isPlaying: isPlaying.value,
    title,
    index,
    totalGuideCount,
    data,
    forwardTo,
    seekTo,
    togglePlay,
    setFollowMode,
    setFreeMode,
    showPlayerControls: showPlayerControls.value,
    togglePlayerControls,
    hidePlayerControls,
    set3XPlayRate,
    resetPlayRate,
    progress,
    refContainer,
    screen,
    commentsBarVisible,
    commentInputVisible,
    commentRef,
    ranges,
    contextMenuPos: pos,
    referenceList,
    refreshReferences,
    markedRanges,
    onClickReference,
    markedReference,
    active: isActive.value,
    durationInFrames,
  };

  return <GuideViewContext value={value}>{children}</GuideViewContext>;
};
