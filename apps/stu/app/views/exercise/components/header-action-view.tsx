"use client";
import { TranslucentGlassButtonExercise } from "@/app/components/common/ask-button";
import { FeedbackType } from "@/app/models/feedback";
import { useChatViewModel } from "@/app/viewmodels/chat/chat-viewmodel";
import { useFeedbackViewModel } from "@/app/viewmodels/feedback/feedback-viewmodel";
import IconFeedback from "@/public/icons/feedback-icon.svg";
import IconAsk from "@/public/icons/icon-ask.svg";
import IconList from "@/public/icons/list.svg";
import { useSearchParams } from "next/navigation";
import { FC } from "react";
import Chat from "../../chat/chat-view";
import { useCourseViewContext } from "../../course/course-view-context";
import FeedbackView from "../../feedback/feedback-view";

export const ExerciseHeaderActionView: FC<{
  widgetIndex?: number;
  questionId: string;
  showAskButton?: boolean;
  showGuideButton?: boolean;
}> = ({
  questionId,
  showAskButton = true,
  showGuideButton = true,
  widgetIndex,
}) => {
  const { setIsProgressBarOpen } = useCourseViewContext();
  const chatViewModel = useChatViewModel({
    questionId: questionId,
    index: widgetIndex,
  });

  const searchParams = useSearchParams();
  const studyType = searchParams.get("studyType");
  const knowledgeId = searchParams.get("knowledgeId");
  const phaseId = searchParams.get("phaseId");
  const lessonId = searchParams.get("lessonId");
  const studySessionId = searchParams.get("studySessionId");
  const subjectId = searchParams.get("subjectId");

  // 使用反馈ViewModel
  const feedbackViewModel = useFeedbackViewModel({
    feedbackType:
      studyType === "1"
        ? FeedbackType.AI
        : studyType === "2"
          ? FeedbackType.Exercise
          : FeedbackType.EXPAND_EXERCISE,
    feedbackKnowledgeId: knowledgeId ? parseInt(knowledgeId) : undefined,
    feedbackPhaseId: phaseId ? parseInt(phaseId) : undefined,
    feedbackLessonId: lessonId ? parseInt(lessonId) : undefined,
    feedbackWidgetIndex: widgetIndex || 0,
    feedbackStudySessionId: studySessionId || "",
    feedbackSubjectId: subjectId ? parseInt(subjectId) : undefined,
  });

  // const handleAsk = () => {
  //   chatViewModel.handleOpen();
  // };
  return (
    <div className="flex flex-row items-center gap-3">
      {showAskButton && (
        <>
          <TranslucentGlassButtonExercise
            icon={<IconAsk className="mr-0.5" />}
            className="inline-flex h-10 items-center justify-center rounded-lg border border-solid border-[rgba(51,46,41,0.12)] bg-transparent text-[rgba(51,48,45,0.7)] opacity-100 shadow-none outline-0"
            onClick={() => chatViewModel.handleOpen()}
          >
            问一问
          </TranslucentGlassButtonExercise>
          <Chat viewModel={chatViewModel} questionId={questionId} />
        </>
      )}
      {showGuideButton && (
        <TranslucentGlassButtonExercise
          icon={<IconList />}
          className="inline-flex size-10 items-center justify-center rounded-lg border border-solid border-[rgba(51,46,41,0.12)] bg-transparent shadow-none outline-0"
          onClick={() => setIsProgressBarOpen(true)}
        ></TranslucentGlassButtonExercise>
      )}
      <TranslucentGlassButtonExercise
        icon={<IconFeedback />}
        className="inline-flex h-10 items-center justify-center rounded-lg border border-solid border-[rgba(51,46,41,0.12)] bg-transparent text-[rgba(51,48,45,0.7)] opacity-100 shadow-none outline-0"
        onClick={() => feedbackViewModel.handleOpen()}
      ></TranslucentGlassButtonExercise>

      {/* 添加反馈弹窗组件 */}
      <FeedbackView viewModel={feedbackViewModel} />
    </div>
  );
};
