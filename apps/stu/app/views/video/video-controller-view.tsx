import { TranslucentGlassButton } from "@/app/components/guide/guide-buttons";
import IconSpin from "@/public/icons/animate-spin.svg";
import IconFastBackward from "@/public/icons/fast-backward.svg";
import IconFastForward from "@/public/icons/fast-forward.svg";
import IconPause from "@/public/icons/pause.svg";
import IconPlay from "@/public/icons/play.svg";
import RectangleBottom from "@/public/icons/rectangle-bottom.svg";
import { useSignal } from "@preact-signals/safe-react";
import { cn } from "@repo/ui/lib/utils";
import { FC, useCallback } from "react";
import { SeekBar } from "./components/seek-bar";
import { useVideoViewContext } from "./video-view-context";

const RateSelector: FC<{ onClose?: () => void }> = ({ onClose }) => {
  const rates = [0.75, 1.0, 1.25, 1.5, 1.75, 2.0];

  const { playRate } = useVideoViewContext();

  const handleRateChange = (rate: number) => {
    playRate.value = rate;
    onClose?.();
  };

  return (
    <div className="absolute -left-1 bottom-16 w-max">
      <ul className="flex w-20 flex-col gap-2 rounded-xl bg-white/80 from-white to-[#FEFDFA] p-1 shadow-[-4px_0px_8px_0px_rgba(64,43,26,0.05)] backdrop-blur-[5px]">
        {rates.map((rate, index) => (
          <ol
            key={index}
            onClick={() => handleRateChange(rate)}
            className={cn(
              "flex h-10 w-full select-none items-center justify-center rounded-lg p-2 text-base font-bold leading-tight transition-colors",
              rate === playRate.value
                ? "bg-[#FF7B59] text-white"
                : "text-text-5"
            )}
          >
            {rate}x
          </ol>
        ))}
      </ul>
      <RectangleBottom className="absolute -bottom-1.5 left-1/2 -translate-x-1/2" />
    </div>
  );
};

export const VideoControllerView: FC = () => {
  const {
    isPlaying,
    forwardTo,
    togglePlay,
    playRate,
    showPlayerControls,
    canPlay,
  } = useVideoViewContext();
  const showPlayRateSelector = useSignal(false);
  const handleForwardTo = useCallback(
    (seconds: number) => {
      forwardTo(seconds);
      // trackEventWithLessonId("doc_seek_10s_click");
    },
    [forwardTo]
  );

  if (!showPlayerControls) {
    return null;
  }
  return (
    <div className="absolute bottom-8 left-0 flex h-12 w-full flex-row gap-3 px-8">
      <div className="relative flex flex-row items-center">
        <TranslucentGlassButton
          className="w-18 h-12"
          onClick={() =>
            (showPlayRateSelector.value = !showPlayRateSelector.value)
          }
        >
          {playRate.value}x
        </TranslucentGlassButton>
        {showPlayRateSelector.value && (
          <RateSelector onClose={() => (showPlayRateSelector.value = false)} />
        )}
      </div>
      <div className="flex-1">
        <SeekBar />
      </div>
      <div className="flex flex-row items-center gap-3">
        <TranslucentGlassButton
          className="size-12"
          onClick={() => handleForwardTo(-10)}
          icon={<IconFastBackward />}
        />
        <TranslucentGlassButton
          className="w-18 h-12"
          onClick={togglePlay}
          icon={
            !canPlay ? (
              <IconSpin className="size-6 animate-spin" />
            ) : isPlaying ? (
              <IconPause />
            ) : (
              <IconPlay />
            )
          }
        />
        <TranslucentGlassButton
          className="size-12"
          onClick={() => handleForwardTo(10)}
          icon={<IconFastForward />}
        />
      </div>
    </div>
  );
};
