import { IconButton } from "@/app/components/guide/guide-buttons";
import IconBack from "@/public/icons/back.svg";
import IconList from "@/public/icons/list.svg";
import { FC, useCallback } from "react";
import { useVideoViewContext } from "./video-view-context";

export const VideoMenuView: FC = () => {
  const { exit, showPlayerControls, setIsProgressBarOpen } =
    useVideoViewContext();

  const handleOpenProgressBar = useCallback(() => {
    setIsProgressBarOpen(true);
  }, [setIsProgressBarOpen]);

  if (!showPlayerControls) {
    return null;
  }

  return (
    <div className="absolute top-8 z-10 flex h-11 w-full flex-row items-center justify-between px-8">
      <IconButton icon={<IconBack />} onClick={exit} />

      <div className="flex flex-row items-center gap-3">
        <IconButton icon={<IconList />} onClick={handleOpenProgressBar} />
      </div>
    </div>
  );
};
