import { WidgetViewProps } from "@/types/app/ui";
import {
  batch,
  ReadonlySignal,
  Signal,
  useComputed,
  useSignal,
  useSignalEffect,
} from "@preact-signals/safe-react";
import { VideoWidgetData } from "@repo/core/types/data/widget-video";

import { VolcenginePlayer } from "@repo/core/components/volcengine-video/volcengine-video";
import { createContext, FC, useCallback, useContext, useEffect } from "react";
import {
  CourseViewContextType,
  useCourseViewContext,
} from "../course/course-view-context";

type VideoViewContextType = CourseViewContextType & {
  title?: string;
  index?: number;
  totalGuideCount: number;
  data: VideoWidgetData;
  refVolcenginePlayer: Signal<VolcenginePlayer | null>;
  canPlay: boolean;
  isPlaying: boolean;
  forwardTo: (seconds: number) => void;
  seekTo: (seconds: number) => void;
  togglePlay: () => void;
  showPlayerControls: boolean;
  togglePlayerControls: () => void;
  playRate: Signal<number>;
  set3XPlayRate: () => void;
  resetPlayRate: () => void;
  duration: number;
  currentTime: ReadonlySignal<number>;
  active: boolean;
};

const VideoViewContext = createContext<VideoViewContextType>(
  {} as VideoViewContextType
);

export const useVideoViewContext = () => {
  return useContext(VideoViewContext);
};

interface VideoViewContextProviderProps extends WidgetViewProps<"video"> {
  children: React.ReactNode;
}

export const VideoViewContextProvider: FC<VideoViewContextProviderProps> = ({
  totalGuideCount,
  content,
  // active,
  children,
}) => {
  const courseContexts = useCourseViewContext();
  const {
    next,
    reportCostTime,
    isVersionChanged,
    videoPlayRate: playRate,
    isProgressBarOpen,
    currentIndex,
  } = courseContexts;
  const { data, name: title, index } = content;
  const { duration } = data;
  const isActive = useComputed(() => {
    return index === currentIndex.value;
  });

  const refVolcenginePlayer = useSignal<VolcenginePlayer | null>(null);
  const canPlay = useSignal(false);
  const isPlaying = useSignal(false);
  const isPlayingBeforeDocumentHidden = useSignal(false);
  const isPlayingBeforeUserAction = useSignal(false);
  const showPlayerControls = useSignal(false);
  const playRateBeforeLongPress = useSignal(1);
  const currentTime = useSignal(0);

  const lastTime = useSignal(Date.now());
  const costTime = useSignal(0);

  const forwardTo = useCallback(
    (seconds: number) => {
      if (!isActive.value) return;
      if (!refVolcenginePlayer.value) {
        return;
      }
      const player = refVolcenginePlayer.value;
      player.seek(player.currentTime + seconds);
      // player.seek(Math.min(player.currentTime + seconds, player.duration));
    },
    [refVolcenginePlayer, isActive.value]
  );
  const seekTo = useCallback(
    (seconds: number) => {
      if (!isActive.value) return;
      if (!refVolcenginePlayer.value) {
        return;
      }
      const player = refVolcenginePlayer.value;
      player.seek(seconds);
    },
    [refVolcenginePlayer, isActive.value]
  );

  const togglePlay = useCallback(() => {
    isPlaying.value = !isPlaying.peek();
  }, [isPlaying]);

  const togglePlayerControls = useCallback(() => {
    showPlayerControls.value = !showPlayerControls.peek();
  }, [showPlayerControls]);

  const set3XPlayRate = useCallback(() => {
    if (playRate.value === 3) return;
    playRateBeforeLongPress.value = playRate.value;
    playRate.value = 3;
  }, [playRate, playRateBeforeLongPress]);

  const resetPlayRate = useCallback(() => {
    if (playRate.value === playRateBeforeLongPress.value) return;
    playRate.value = playRateBeforeLongPress.value;
  }, [playRate, playRateBeforeLongPress]);

  useEffect(() => {
    const player = refVolcenginePlayer.value;
    if (!player) return;

    const handleEnded = () => {
      next();
    };
    const handleWaiting = () => {
      canPlay.value = false;
    };
    const handlePlaying = () => {
      canPlay.value = true;
    };
    const handleCanplay = () => {
      canPlay.value = true;
    };
    const handleError = () => {
      canPlay.value = false;
    };
    const handleTimeUpdate = () => {
      currentTime.value = player.currentTime;
    };
    const handleSeeked = () => {
      isPlaying.value = true;
    };
    player.addEventListener("ended", handleEnded);
    player.addEventListener("seeked", handleSeeked);
    player.addEventListener("waiting", handleWaiting);
    player.addEventListener("playing", handlePlaying);
    player.addEventListener("canplay", handleCanplay);
    player.addEventListener("error", handleError);
    player.addEventListener("timeupdate", handleTimeUpdate);
    return () => {
      player.removeEventListener("ended", handleEnded);
      player.removeEventListener("seeked", handleSeeked);
      player.removeEventListener("waiting", handleWaiting);
      player.removeEventListener("playing", handlePlaying);
      player.removeEventListener("canplay", handleCanplay);
      player.removeEventListener("error", handleError);
      player.removeEventListener("timeupdate", handleTimeUpdate);
    };
  }, [
    next,
    refVolcenginePlayer.value,
    isActive,
    isPlaying,
    canPlay,
    currentTime,
  ]);

  useEffect(() => {
    const handleVisibilitychange = () => {
      if (document.visibilityState === "hidden") {
        batch(() => {
          isPlayingBeforeDocumentHidden.value = isPlaying.peek();
          isPlaying.value = false;
        });
      } else {
        isPlaying.value = isPlayingBeforeDocumentHidden.peek();
      }
    };

    document.addEventListener("visibilitychange", handleVisibilitychange);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilitychange);
    };
  }, [isPlaying, isPlayingBeforeDocumentHidden]);

  useEffect(() => {
    if (isProgressBarOpen) {
      batch(() => {
        isPlayingBeforeUserAction.value = isPlaying.peek();
        isPlaying.value = false;
      });
    } else {
      isPlaying.value = isPlayingBeforeUserAction.peek();
    }
  }, [isProgressBarOpen, isPlaying, isPlayingBeforeUserAction]);

  useSignalEffect(() => {
    if (isActive.value) {
      isPlaying.value = true;
    } else {
      isPlaying.value = false;
    }
  });

  useSignalEffect(() => {
    if (refVolcenginePlayer.value === null) return;

    if (isActive.value && isPlaying.value && canPlay.value) {
      refVolcenginePlayer.value?.play();
    } else {
      refVolcenginePlayer.value?.pause();
    }
  });

  useSignalEffect(() => {
    if (isVersionChanged.value) {
      isPlaying.value = false;
    }
  });

  useEffect(() => {
    if (isActive.value) {
      lastTime.value = Date.now();
    }
  }, [isActive.value, lastTime]);

  useEffect(() => {
    if (!isActive.value) return;

    const updateTime = () => {
      const now = Date.now();
      costTime.value += now - lastTime.value;
      lastTime.value = now;
      reportCostTime(costTime.value);
    };
    const timer = setInterval(updateTime, 250);
    return () => {
      clearInterval(timer);
      updateTime();
    };
  }, [isActive.value, reportCostTime, lastTime, costTime]);

  const value = {
    ...courseContexts,
    totalGuideCount,
    title,
    index,
    data,
    refVolcenginePlayer,
    canPlay: canPlay.value,
    isPlaying: isPlaying.value,
    showPlayerControls: showPlayerControls.value,
    forwardTo,
    seekTo,
    togglePlay,
    togglePlayerControls,
    set3XPlayRate,
    resetPlayRate,
    playRate,
    currentTime,
    duration,
    active: isActive.value,
  };

  return <VideoViewContext value={value}>{children}</VideoViewContext>;
};
