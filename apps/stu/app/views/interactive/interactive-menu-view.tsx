"use client";
import { IconButton } from "@/app/components/guide/guide-buttons";
import IconBack from "@/public/icons/back.svg";
import IconList from "@/public/icons/list.svg";
import { FC } from "react";
import { useCourseViewContext } from "../course/course-view-context";

export const InteractiveMenuView: FC = () => {
  const { setIsProgressBarOpen, exit } = useCourseViewContext();

  return (
    <div className="pointer-events-none absolute top-8 z-10 flex h-11 w-full flex-row items-center justify-between px-8">
      <IconButton icon={<IconBack />} onClick={exit} />
      <div className="pointer-events-auto flex flex-row items-center gap-3">
        <IconButton
          icon={<IconList />}
          onClick={() => setIsProgressBarOpen(true)}
        />
      </div>
    </div>
  );
};
