"use client";
import {
  createContext,
  FC,
  ReactNode,
  RefObject,
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";

import { useSchoolType } from "@/app/models/comments/comments-model";
import { exitLesson, trackEvent } from "@/app/utils/device";
import {
  CourseSequenceViewmodel,
  useCourseSequenceViewmodel,
} from "@/app/viewmodels/course/course-sequence-vm";
import {
  Signal,
  useComputed,
  useSignal,
  useSignalEffect,
} from "@preact-signals/safe-react";
import { GuideMode } from "@repo/core/types/data/widget-guide";

type CourseViewContextType = CourseSequenceViewmodel & {
  isProgressBarOpen: boolean;
  setIsProgressBarOpen: (isProgressBarOpen: boolean) => void;
  guideMode: Signal<GuideMode>;
  showSubtitle: boolean;
  setShowSubtitle: (showSubtitle: boolean) => void;
  playRate: Signal<number>; //文稿播放速率
  videoPlayRate: Signal<number>; //视频播放速率
  exit: () => void;
  trackEventWithLessonId: (eventID: string, needWidgetInfo?: boolean) => void;
  subjectId: number;
  knowledgeName: string;
  lessonName: string;
  redirectCommentId?: string;
  redirectCommentRootId?: string;
  redirectReferenceId?: string;
  hadShownRedirectComment: Signal<boolean>;
  studySessionId: number;
  studyType: number;
  refCourseContainer: RefObject<HTMLDivElement | null>;
  exerciseCompletedRecord: Signal<{
    [key: number]: boolean;
  }>;
  refWidgets: Signal<HTMLDivElement[]>;
  enableComment: boolean;
};

const CourseViewContext = createContext<CourseViewContextType>(
  {} as CourseViewContextType
);

const useCourseViewContext = () => useContext(CourseViewContext);

interface CourseViewProviderProps {
  knowledgeId: number;
  subjectId: number;
  knowledgeName: string;
  lessonName: string;
  children: ReactNode;
  redirectWidgetIndex?: string;
  redirectCommentId?: string;
  redirectCommentRootId?: string;
  redirectReferenceId?: string;
  studySessionId: number;
  studyType: number;
}

const CourseViewProvider: FC<CourseViewProviderProps> = ({
  knowledgeId,
  subjectId,
  knowledgeName,
  lessonName,
  children,
  redirectCommentId,
  redirectCommentRootId,
  redirectReferenceId,
  redirectWidgetIndex,
  studySessionId,
  studyType,
}) => {
  const refCourseContainer = useRef<HTMLDivElement>(null);
  const refWidgets = useSignal<HTMLDivElement[]>([]);
  // 获取数据及操作数据的方法
  const sequenceVm = useCourseSequenceViewmodel(
    knowledgeId,
    redirectWidgetIndex
  );

  const {
    lessonId,
    currentWidget,
    currentIndex,
    scrollBehavior,
    lessonVersion,
  } = sequenceVm;
  const [isProgressBarOpen, setIsProgressBarOpen] = useState(false);
  const guideMode = useSignal(GuideMode.follow);
  const [showSubtitle, setShowSubtitle] = useState(true);
  const playRate = useSignal(1);
  const videoPlayRate = useSignal(1);
  const hadShownRedirectComment = useSignal(false);
  const exerciseCompletedRecord = useSignal<{
    [key: number]: boolean;
  }>({});

  const scrollTargetWidget = useComputed(() => {
    return refWidgets.value[currentIndex.value];
  });

  const trackEventWithLessonId = useCallback(
    (eventID: string, needWidgetInfo?: boolean) => {
      const map = needWidgetInfo
        ? {
            widgetIndex: currentWidget?.index,
            widgetType: currentWidget?.type,
          }
        : {};
      trackEvent(eventID, {
        lesson_id: lessonId,
        ...map,
      });
    },
    [lessonId, currentWidget]
  );

  const exit = useCallback(() => {
    trackEventWithLessonId("lesson_exit_click", true);
    console.log("exit");
    exitLesson();
  }, [trackEventWithLessonId]);

  useSignalEffect(() => {
    const ref = scrollTargetWidget.value;
    if (!ref) return;

    refCourseContainer.current?.scrollTo({
      top: ref.offsetTop,
      behavior: scrollBehavior.value,
    });
    console.count(`next::scrollBehavior::${ref?.dataset.name}`);
  });

  useEffect(() => {
    const handler = () => {
      const index = currentIndex.value;
      console.log("next::scrollBehavior resize", index);
      refCourseContainer.current?.scrollTo({
        top: window.innerHeight * index,
        behavior: "instant",
      });
    };
    window.addEventListener("resize", handler);
    return () => window.removeEventListener("resize", handler);
  }, [currentIndex]);

  useEffect(() => {
    const handler = () => {
      if (isProgressBarOpen) {
        setIsProgressBarOpen(false);
      }
    };
    document.body.addEventListener("click", handler);

    return () => {
      document.body.removeEventListener("click", handler);
    };
  }, [isProgressBarOpen]);

  const { data, isLoading } = useSchoolType();

  const value = {
    ...sequenceVm,
    isProgressBarOpen,
    setIsProgressBarOpen,
    guideMode,
    showSubtitle,
    setShowSubtitle,
    playRate,
    videoPlayRate,
    exit,
    trackEventWithLessonId,
    subjectId,
    knowledgeName,
    lessonName,
    redirectCommentId,
    redirectCommentRootId,
    redirectReferenceId,
    hadShownRedirectComment,
    studySessionId,
    studyType,
    refCourseContainer,
    exerciseCompletedRecord,
    refWidgets,
    lessonVersion,
    enableComment: !isLoading && data?.schoolType !== 2,
  };
  return <CourseViewContext value={value}>{children}</CourseViewContext>;
};

export {
  CourseViewContext,
  CourseViewProvider,
  GuideMode,
  useCourseViewContext,
  type CourseViewContextType,
};
