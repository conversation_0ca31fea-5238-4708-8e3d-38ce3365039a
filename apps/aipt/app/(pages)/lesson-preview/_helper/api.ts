import { get as courseCenterGet } from "../../../../lib/course-center-fetcher";
import { get } from "../../../../lib/fetcher";

const transformWidget = (item: any) => {
  return {
    index: item.widgetIndex,
    name: item.widgetName,
    type: item.widgetType,
    status: item.widgetType,
    hidden: item.hidden,
  };
};

// 获取当前可课的所有组件
export const getWidgets = async (
  knowledgeId: number,
  type: "draft" | "official" = "draft"
) => {
  let widgets = [];
  if (type === "draft") {
    const res: any = await get(`/api/v1/lesson_widget/preview/progress`, {
      query: { lessonId: String(knowledgeId) },
    });
    widgets = res.lessonWidgets.map((item: any) => transformWidget(item));
  } else {
    const res: any = await courseCenterGet(`/api/v1/lesson/preview/progress`, {
      query: { lessonId: String(knowledgeId) },
    });
    widgets = res.widgetList.map((item: any) => transformWidget(item));
  }
  return widgets;
};


const getExercise = async (lessonId: number, widgetIndex: number) => {
  const res: any = await get(`/api/v1/lesson_widget/question/get`, {
    query: { lessonId: String(lessonId), widgetIndex: String(widgetIndex) },
  });

  let questionList = res.questionList.flat().filter((item: any) => item);
  questionList = questionList.map((item: any) => ({
    questionInfo: {
      ...item,
      // questionContent: item.questionContent.questionStem,
    },
    // questionInfo: transformQuestionData2(item),
    // studentAnswer: item.studentAnswer,
    // answerStats: item.answerStats,
  }));
  // res.data = JSON.parse(res.data);
  //   setWidgetData({
  //     questionList,
  //     widgetType: "exercise",
  //   });
  return questionList;
};

export const getWidget = async (
  type: "draft" | "official",
  lessonId: number,
  widgetIndex: number
) => {
  console.log("获取详情", widgetIndex);

  const query = { lessonId: String(lessonId), widgetIndex: String(widgetIndex) };
  let res: any;
  if (type === "draft") {
    res = await get(`/api/v1/lesson_widget/preview/info`, { query });
    if (res.widgetType === "exercise") {
      res.data = await getExercise(lessonId, widgetIndex);
    } else {
      res.data = JSON.parse(res.data);
    }
    // console.log(77777, res);

    // console.log(88888, res.data);

    // console.log(99999, res.data);
  } else {
    res = await courseCenterGet(`/api/v1/lesson/preview/widget/info`, {
      query,
    });
  }

  console.log(111222, res);

  //   setWidgetData(res);
  return res;
};
