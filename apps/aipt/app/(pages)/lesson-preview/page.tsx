"use client"

import { saveTokenFormUrl } from '@/lib/login-helper';
import LessonPreview from "@repo/core/components/lesson-preview";
import { useRequest } from 'ahooks';
import { useSearchParams } from 'next/navigation';
import { Suspense, useState } from 'react';
import { Toaster } from "sonner";
import { getWidget, getWidgets } from './_helper/api';

const Preview: React.FC = () => {
    saveTokenFormUrl();
    const searchParams = useSearchParams()
    const id = Number(searchParams.get('id'))
    const type = searchParams.get('type') as "draft" | "official" || 'draft';
    const { data: widgets, loading } = useRequest(async () => getWidgets(id));
    const [active, setActive] = useState(0);
    const { data: widget, loading: getWidgetLoading } = useRequest(async () => getWidget(type, id, active), {
        refreshDeps: [active],
    });

    return (
        <div className="bg-white h-screen w-screen">
            <header className="h-16 flex-none bg-white px-3 outline-1 outline-gray-300">
                <nav className="flex h-full w-full flex-row items-center gap-6 border-b border-b-gray-200 shadow-[0_1px_0_0_rgba(0,0,0,0.04)]">
                    <div className="text-xl">课程预览</div>
                </nav>
            </header>

            <div className="flex flex-col h-full w-full p-2">
                <div className="lesson_detail_container h-full w-full">
                    {loading ? <div>loading...</div> :
                        <LessonPreview
                            widgets={widgets}
                            active={active}
                            setActive={setActive}
                            widget={widget}
                            loading={getWidgetLoading}
                            simulation={false}
                        />}
                </div>
            </div>
        </div>
    );
};

const PreviewPage = () => (
    <Suspense fallback={<div>Loading...</div>}>
        <Preview />
        <Toaster position="top-center" />
    </Suspense>
)
export default PreviewPage;